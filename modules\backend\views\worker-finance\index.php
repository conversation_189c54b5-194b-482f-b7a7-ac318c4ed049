<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use app\modules\backend\models\WorkerFinances;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\common\models\Tracking;

DataTablesAsset::register($this);

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

$salaryText = Yii::t('app', 'salary');
$advanceText = Yii::t('app', 'advance');
$all = Yii::t('app', 'all');
$deleteText = Yii::t('app', 'delete');

$this->title = Yii::t('app', 'worker_finance_section');

?>

<style>
       /* Стили для конкретного select2 */
       #worker_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #worker_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
</style>


<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-4">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-8">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('accountant')): ?>
                <div class="d-flex justify-content-end align-items-center gap-2">

                    <a href="#" class="btn btn-success export-monthly-report" data-toggle="modal" data-target="#ideal-mini-modal-excel">
                        <i class="fas fa-file-excel"></i> <?= Yii::t('app', 'export_excel') ?>
                    </a>
                    <!-- Date inputs -->
                    <div class="d-flex gap-2" style="min-width: 200px;">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                    <!-- Worker filter dropdown -->
                    <select id="worker_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'workers') ?></option>
                        <?php foreach($workers as $worker): ?>
                            <option value="<?= $worker->id ?>"><?= $worker->full_name ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Filter button -->

                    <button type="button" class="btn btn-primary" id="search-button">
                        <?= Yii::t('app', 'search') ?>
                    </button>

                   

                    <!-- Pay salary button -->
                    <a href="#" class="btn btn-primary worker-finance-create" data-toggle="modal" data-target="#ideal-mini-modal">
                        <?= Yii::t('app', 'pay_salary') ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>


    <?php Pjax::begin(['id' => 'worker_finance-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="worker_finance-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "worker_name") ?></th>
                    <th><?= Yii::t("app", "position") ?></th>
                    <th><?= Yii::t("app", "month") ?></th>
                    <th><?= Yii::t("app", "salary") ?></th>
                    <th><?= Yii::t("app", "amount_paid") ?></th>
                    <th><?= Yii::t("app", "remaining_salary") ?></th>
                    <th><?= Yii::t("app", "type") ?></th>
                    <th><?= Yii::t("app", "description") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['worker_name']) ?></td>
                        <td><?= Html::encode($model['position']) ?></td>
                        <td><?= Html::encode($model['month']) ?></td>
                        <td><?= in_array($model['type'], [WorkerFinances::TYPE_DEBT, WorkerFinances::TYPE_BONUS, WorkerFinances::TYPE_ONE_TIME_PAYMENT]) ? '-' : number_format($model['salary'], 0, ',', ' ') ?></td>
                        <td><?= number_format($model['amount'], 0, ',', ' ') ?></td>
                        <td><?= in_array($model['type'], [WorkerFinances::TYPE_DEBT, WorkerFinances::TYPE_BONUS, WorkerFinances::TYPE_ONE_TIME_PAYMENT]) ? '-' : number_format($model['remaining_salary'], 0, ',', ' ') ?></td>
                        <td>
                            <?php
                            if ($model['type'] == WorkerFinances::TYPE_SALARY) {
                                echo Yii::t('app', 'salary');
                            } elseif ($model['type'] == WorkerFinances::TYPE_ADVANCE) {
                                echo Yii::t('app', 'advance');
                            } elseif ($model['type'] == WorkerFinances::TYPE_BONUS) {
                                echo Yii::t('app', 'bonus');
                            } elseif ($model['type'] == WorkerFinances::TYPE_DEBT)
                            {
                                echo Yii::t('app', 'debt');
                            } elseif ($model['type'] == WorkerFinances::TYPE_ONE_TIME_PAYMENT) {
                                echo Yii::t('app', 'one_time_payment');
                            }
                            ?>
                        </td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td><?= date('d.m.Y H:i', strtotime($model['created_at'])) ?></td>

                        <?php 
                            $tracking = null;
                            // Получаем последнюю запись для этого работника
                            $lastRecord = null;
                            foreach ($result as $record) {
                                if ($record['worker_id'] === $model['worker_id']) {
                                    $lastRecord = $record;
                                    break; // Берем первую запись, так как они отсортированы по id DESC
                                }
                            }
                            
                            if ($lastRecord && $lastRecord['id'] === $model['id']) {
                                $tracking = Tracking::find()
                                    ->where(['process_id' => $model['id']])
                                    ->andWhere(['progress_type' => Tracking::PAY_FOR_WORKER])
                                    ->andWhere(['is', 'accepted_at', null])
                                    ->andWhere(['is', 'deleted_at', null])
                                    ->one();
                            }
                        ?>
                        <?php if ($tracking): ?>
                            <td>
                                <a class="dropdown-item worker-finance-delete badge badge-info" href="#" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= $model['id'] ?>">
                                    <i class="fas fa-trash"></i> <?= Yii::t('app', 'delete') ?>
                                </a>
                            </td>
                        <?php else: ?>
                        <td></td>
                        <?php endif; ?>

                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "salary_create") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "salary_update") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "salary_delete") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "export_excel") ?>"></div>







<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var salaryText = "{$salaryText}";
    var advanceText = "{$advanceText}";
    var all = "{$all}";
    var deleteText = "{$deleteText}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#worker_finance-grid-view')) {
            $('#worker_finance-grid-view').DataTable().destroy();
        }
        
        $('#worker_finance-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[8, 'desc']], // Sort by created_at column by default
            "columnDefs": [
                {
                    "targets": [3, 4, 5],
                    "render": function(data, type, row) {
                        if (type === 'display') {
                            return data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                        }
                        return data;
                    }
                },
                {
                    "targets": 8,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            var timePart = parts.length > 1 ? parts[1].replace(':', '') : '0000';
                            return dateParts[2] + dateParts[1] + dateParts[0] + timePart;
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeSearch() {
        $('#search-button').on('click', function() {
            var startDate = $('#date_from').val();
            var endDate = $('#date_to').val();
            var workerId = $('#worker_filter').val();

            $.ajax({
                url: '/backend/worker-finance/search',
                type: 'POST',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                    worker_id: workerId
                },
                success: function(response) {
                    if (response.status === 'success') {
                        var table = $('#worker_finance-grid-view').DataTable();
                        table.clear();
                        
                        response.data.forEach(function(row) {
                            var actionColumn = '';
                            
                            // Находим последнюю запись для этого работника
                            var lastRecord = response.data.find(function(record) {
                                return record.worker_id === row.worker_id;
                            });
                            
                            // Проверяем tracking только для последней записи
                            if (lastRecord && lastRecord.id === row.id && 
                                row.tracking && 
                                row.tracking.process_id === row.id && 
                                row.tracking.progress_type === 7 && 
                                row.tracking.accepted_at === null && 
                                row.tracking.deleted_at === null) {
                                actionColumn = '<a class="dropdown-item worker-finance-delete badge badge-info" href="#" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="' + row.id + '">' +
                                    '<i class="fas fa-trash"></i> ' + deleteText + '</a>';
                            }
                            
                            table.row.add([
                                row.worker_name,
                                row.position,
                                row.month,
                                row.type == 1 ? row.salary : row.type == 4 ? '-' : row.salary,
                                row.amount,
                                row.type == 1 ? row.remaining_salary : row.type == 4 ? '-' : row.remaining_salary,
                                row.type == 1 ? salaryText : row.type == 2 ? advanceText : row.type == 3 ? 'bonus' : 'debt',
                                row.description,
                                moment(row.created_at).format('DD.MM.YYYY HH:mm'),
                                actionColumn
                            ]);
                        });
                        
                        table.draw();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                }
            });
        });
    }

    function initializeWorkerFinanceCreate() {
        $(document).off('click.worker-finance-create').on('click.worker-finance-create', '.worker-finance-create', function() {
            $.ajax({
                url: '/backend/worker-finance/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-finance-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-finance-create-button').on('click.worker-finance-create-button', '.worker-finance-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#worker-finance-create-form').serialize();
                $.ajax({
                    url: '/backend/worker-finance/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#worker_finance-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            }); 

                            if (response.message) {
                                $('#form-error-message').text(response.message).css('color', 'red').show();
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

   

    function initializeWorkerFinanceDelete() {
        $(document).off('click.worker-finance-delete').on('click.worker-finance-delete', '.worker-finance-delete', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/worker-finance/delete',
                data: { id: id },
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(three);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("worker-finance-delete-button");
                    } else {
                        $('#ideal-mini-modal-delete .modal-body').prepend(
                            '<div class="alert alert-danger">' + 
                            (response.errors || 'Произошла ошибка при загрузке данных') + 
                            '</div>'
                        );
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    $('#ideal-mini-modal-delete .modal-body').prepend(
                        '<div class="alert alert-danger">Произошла ошибка при загрузке данных</div>'
                    );
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-finance-delete-button').on('click.worker-finance-delete-button', '.worker-finance-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#worker-finance-delete-form').serialize();
                $.ajax({
                    url: '/backend/worker-finance/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#worker_finance-grid-pjax'});
                            initializeDataTable();
                        } else {
                            var errorMessage = response.message || response.errors || 'Произошла ошибка при удалении';
                            $('#worker-finance-delete-form').prepend(
                                '<div class="alert alert-danger">' + errorMessage + '</div>'
                            );
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        $('#worker-finance-delete-form').prepend(
                            '<div class="alert alert-danger">Произошла ошибка при удалении</div>'
                        );
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }




        function initializeExportMonthlyReportCreate() {
            $(document).off('click.export-monthly-report').on('click.export-monthly-report', '.export-monthly-report', function(e) {
                e.preventDefault();
                $.ajax({
                    url: '/backend/worker-finance/export-monthly-report',
                    dataType: 'json',
                    type: 'GET',
                    success: function(response) {
                        console.log('AJAX Success:', response);
                        $('#ideal-mini-modal-excel .modal-title').html(four);
                        $('#ideal-mini-modal-excel .modal-body').html(response.content);
                        $('#ideal-mini-modal-excel .mini-button').addClass("monthly-report-create-button");
                        $('#ideal-mini-modal-excel').modal('show');
                        initializeSelect2();
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            });

        $(document).off('click.monthly-report-create-button').on('click.monthly-report-create-button', '.monthly-report-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#export-form').serialize();

                var form = $('<form>')
                    .attr('method', 'post')
                    .attr('action', '/backend/worker-finance/export-monthly-report')
                    .css('display', 'none');

                $.each(formData.split('&'), function(index, field) {
                    var pair = field.split('=');
                    $('<input>').attr({
                        type: 'hidden',
                        name: decodeURIComponent(pair[0]),
                        value: decodeURIComponent(pair[1] || '')
                    }).appendTo(form);
                });

                var csrfToken = $('meta[name="csrf-token"]').attr("content");
                $('<input>').attr({
                    type: 'hidden',
                    name: '_csrf',
                    value: csrfToken
                }).appendTo(form);

                $('body').append(form);
                form.submit(); // Отправляем форму, браузер обработает скачивание

                button.prop('disabled', false);
                $('.close').trigger('click');
                setTimeout(function() {
                    $.pjax.reload({
                        container: '#worker_finance-grid-pjax',
                        complete: function() {
                            initializeDataTable();
                             // Включаем кнопку после завершения
                        }
                    });
                }, 1000); // Задержка для завершения скачивания

                form.remove(); // Удаляем форму после отправки
            }
        });
    }

    function initializeSelect2() {
        $('.select2').each(function() {
            var parentModal = $(this).closest('.modal');
            $(this).select2({
                width: '100%',
                language: {
                    noResults: function() {
                        return "<?= Yii::t('app', 'No results found') ?>";
                    },
                    searching: function() {
                        return "<?= Yii::t('app', 'Searching...') ?>";
                    }
                },
                allowClear: true,
                placeholder: function() {
                    return $(this).find('option:first').text();
                },
                dropdownParent: parentModal.length ? parentModal : $(document.body)
            });
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeSearch();
        initializeSelect2();
        initializeWorkerFinanceCreate();
        initializeWorkerFinanceDelete();
        initializeExportMonthlyReportCreate();
    }
    
    $(document).ready(initializeAll);
    $(document).on('pjax:complete', initializeAll);
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>

