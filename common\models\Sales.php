<?php

namespace app\common\models;

use app\modules\backend\models\Users;
use Yii;

/**
 * This is the model class for table "sales".
 *
 * @property int $id
 * @property int $client_id
 * @property int $sell_user_id
 * @property int|null $confirm_user_id
 * @property int $status
 * @property float $total_sum
 * @property float $total_special_prices_sum
 * @property string|null $created_at
 * @property string|null $driver
 * @property string|null $car_number
 * @property string|null $deleted_at
 * @property string|null $completed_at
 * @property string|null $started_at
 * @property string|null $print_number Номер корешка для печатной формы
 *
 * @property Client $client
 * @property Users $confirmUser
 * @property SalesDetail[] $salesDetails
 * @property Users $sellUser
 */
class Sales extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sales';
    }

    const STATUS_NEW = 1;
    const STATUS_IN_PROGRESS = 2;
    const STATUS_CONFIRMED = 3;

    public static function getStatusList($status = null)
    {
        $statuses = [
            self::STATUS_NEW => '<span style="color:red">' . Yii::t('app', 'new_sales') . '</span>',
            self::STATUS_IN_PROGRESS => '<span style="color:blue">' . Yii::t('app', 'in_progress_sales') . '</span>',
            self::STATUS_CONFIRMED => '<span style="color:green">' . Yii::t('app', 'confirmed_sales') . '</span>',
        ];

        return $status !== null ? $statuses[$status] : $statuses;
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'sell_user_id', 'status', 'total_sum'], 'required'],
            [['client_id', 'sell_user_id', 'confirm_user_id', 'driver', 'car_number'], 'default', 'value' => null],
            [['client_id', 'sell_user_id', 'confirm_user_id', 'status'], 'integer'],
            [['total_sum', 'total_special_prices_sum'], 'number'],
            [['created_at', 'deleted_at', 'completed_at', 'started_at'], 'safe'],
            [['driver', 'car_number', 'print_number'], 'string', 'max' => 50],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
            [['sell_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['sell_user_id' => 'id']],
            [['confirm_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['confirm_user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => 'Client ID',
            'sell_user_id' => 'Sell User ID',
            'confirm_user_id' => 'Confirm User ID',
            'status' => 'Status',
            'total_sum' => 'Total Sum',
            'total_special_prices_sum' => 'Total Special Prices Sum',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
            'print_number' => 'Номер корешка',
        ];
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }

    /**
     * Gets query for [[ConfirmUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getConfirmUser()
    {
        return $this->hasOne(Users::class, ['id' => 'confirm_user_id']);
    }

    /**
     * Gets query for [[SalesDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSalesDetails()
    {
        return $this->hasMany(SalesDetail::class, ['sale_id' => 'id']);
    }

    /**
     * Gets query for [[SellUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSellUser()
    {
        return $this->hasOne(Users::class, ['id' => 'sell_user_id']);
    }

    public function getSalesBonus()
    {
        return $this->hasMany(SalesBonus::class, ['sale_id' => 'id']);
    }

    public function getBonus()
    {
        return $this->hasMany(SalesBonus::class, ['sale_id' => 'id'])
            ->andWhere(['deleted_at' => null]);
    }
}
