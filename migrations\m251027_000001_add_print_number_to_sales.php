<?php

use yii\db\Migration;

/**
 * Class m250127_000001_add_print_number_to_sales
 */
class m251027_000001_add_print_number_to_sales extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('sales', 'print_number', $this->string(50)->null());
        
        // Создаем индекс для быстрого поиска по номеру корешка
        $this->createIndex('idx-sales-print_number', 'sales', 'print_number');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('idx-sales-print_number', 'sales');
        $this->dropColumn('sales', 'print_number');
    }
} 