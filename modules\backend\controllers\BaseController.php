<?php

namespace app\modules\backend\controllers;

use Yii;
use yii\web\Controller;
use yii\filters\AccessControl;
use yii\web\ForbiddenHttpException;

class BaseController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],  
                    ],
                ],
                'denyCallback' => function () {
                    if (Yii::$app->user->isGuest) {
                        return $this->redirect(['/site/login']);
                    }
                    throw new ForbiddenHttpException('Доступ запрещен.');
                },
            ],
        ];
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        // Если пользователь админ - разрешаем всё
        if (Yii::$app->user->can('admin')) {
            return true;
        }

        // Для роли sales разрешаем только SalesInvoiceController
        if (Yii::$app->user->can('sales')) {

            $allowedControllers = [
                'sales-invoice',
                'print-invoice',
                'client'
            ];
            
            if (in_array($this->id, $allowedControllers)) {
                return true;
            }

            // Если уже находимся в SalesInvoiceController, разрешаем доступ
            if ($this instanceof \app\modules\backend\controllers\SalesInvoiceController) {
                return true;
            }
            
            // Если пытаемся зайти в другой контроллер, перенаправляем на SalesInvoiceController/index
            Yii::$app->response->redirect(['/backend/sales-invoice/index']);
            return false;
        }

        // Для роли accountant разрешаем доступ к финансовым контроллерам
        if (Yii::$app->user->can('accountant')) {
            $allowedControllers = [
                'expenses',
                'expenses-type', 
                'worker',
                'worker-finance',
                'archive-worker',
                'position'
            ];
            
            if (in_array($this->id, $allowedControllers)) {
                return true;
            }
            
            // Если пытаемся зайти в другой контроллер, перенаправляем на expenses/index
            Yii::$app->response->redirect(['/backend/expenses/index']);
            return false;
        }

        // Для роли technician разрешаем доступ к оборудованию
        if (Yii::$app->user->can('technical_staff')) {
            $allowedControllers = [
                'equipment',
                'equipment-part',
                'material-defect'
            ];
            
            if (in_array($this->id, $allowedControllers)) {
                return true;
            }
            
            // Если пытаемся зайти в другой контроллер, перенаправляем на equipment/index   
            Yii::$app->response->redirect(['/backend/equipment/index']);
            return false;
        }

      // Для роли product_keeper разрешаем доступ ко всем связанным разделам
        if (Yii::$app->user->can('product_keeper')) {
            $allowedControllers = [
                // Клиенты
                'client',
                'client-special-prices',

                // Продажи
                'sales',

                // Финансы
                'currency',
                'cashbox',
                'payment',
                'cashier',
                'expenses',        // Добавляем доступ к расходам
                'expenses-type',   // Добавляем доступ к типам расходов

                // Склад
                'product',
                'material-income',
                'material-defect',

                // Настройки
                'user',
                'region',

                // Рабочие
                'worker',
                'worker-finance',
                'archive-worker',
                'position',

                // Инвойсы
                'invoice',
                'print-invoice'
            ];

            if (in_array($this->id, $allowedControllers)) {
                return true;
            }

            // Если пытаемся зайти в другой контроллер, перенаправляем на product/index
            Yii::$app->response->redirect(['/backend/product/index']);
            return false;
        }



        

        $permissionName = $this->id . '/' . $action->id;
        if (Yii::$app->user->can($permissionName)) {
            return true;
        }

        throw new ForbiddenHttpException('У вас нет прав для выполнения этого действия.');
    }
}

