<?php
use yii\helpers\Html;
?>

<div class="print-invoice-view">
    <style>
        .print-invoice-view {
            max-width: 500px;
            max-height: 800px;
            margin: 10px auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.3;
            background: white;
            padding: 15px;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            box-sizing: border-box;
        }
        .print-invoice-header {
            text-align: left;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .print-invoice-number {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 6px;
        }
        .editable-number {
            border: none;
            background: transparent;
            font-size: 16px;
            font-weight: bold;
            text-align: left;
            width: 50px;
        }
        .date-line {
            font-size: 11px;
            margin-bottom: 10px;
        }
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .products-table th,
        .products-table td {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: center;
            font-size: 11px;
        }
        .products-table th {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        .products-table td:first-child {
            text-align: left;
            font-size: 11px;
        }
        /* Газированные продукты – чёрный фон, белый текст */
        .products-table tr.gaz-row td {
            background: #000;
            color: #fff;
        }
        .client-info {
            margin-bottom: 15px;
            font-size: 11px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        .total-info {
            text-align: left;
            font-weight: bold;
            margin-top: 15px;
            border-top: 2px solid #000;
            padding-top: 10px;
            font-size: 13px;
        }
        .prices-section {
            margin-top: 15px;
            font-size: 11px;
        }
        .price-sum {
            text-align: left;
            font-weight: bold;
            margin-bottom: 3px;
            padding: 3px;
            border-bottom: 1px solid #ccc;
        }
        .no-print {
            margin-top: 15px;
            text-align: center;
        }
        @media print {
            @page {
                size: A4 portrait;
                margin: 5mm;
            }

            @page:first {
                size: A4 portrait;
                margin: 5mm;
            }

            * {
                visibility: hidden;
                font-weight: bold !important;
            }

            .print-invoice-view,
            .print-invoice-view * {
                visibility: visible;
                font-weight: bold !important;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
                width: 100vw !important;
                height: 100vh !important;
                max-height: 240mm !important;
                overflow: hidden !important;
                font-weight: bold !important;
            }

            .modal,
            .modal-dialog,
            .modal-content,
            .modal-body {
                all: unset !important;
                display: block !important;
                position: static !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                border: none !important;
                box-shadow: none !important;
                font-weight: bold !important;
            }

            .print-invoice-view {
                position: absolute !important;
                top: 0 !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                margin: 0 !important;
                padding: 2mm !important;
                width: 35% !important;
                max-width: 35% !important;
                height: 240mm !important;
                max-height: 240mm !important;
                background: white !important;
                box-shadow: none !important;
                border: none !important;
                font-size: 14px !important;
                line-height: 1.3 !important;
                display: block !important;
                overflow: hidden !important;
                page-break-after: avoid !important;
                page-break-inside: avoid !important;
                break-after: avoid !important;
                break-inside: avoid !important;
                box-sizing: border-box !important;
                font-weight: bold !important;
                text-align: left !important;
            }

            .print-invoice-view * {
                page-break-after: avoid !important;
                page-break-inside: avoid !important;
                break-after: avoid !important;
                break-inside: avoid !important;
                font-weight: bold !important;
            }

            .no-print {
                display: none !important;
            }
            
            body {
                page-break-after: avoid !important;
                overflow: hidden !important;
            }
            
            .print-invoice-header {
                text-align: left !important;
                margin-bottom: 12px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                border-bottom: 2px solid #000 !important;
                padding-bottom: 8px !important;
                font-weight: bold !important;
            }

            .print-invoice-number {
                font-size: 22px !important;
                font-weight: bold !important;
                margin-bottom: 8px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                text-align: left !important;
            }

            .editable-number {
                font-size: 20px !important;
                font-weight: bold !important;
                border: none !important;
                background: transparent !important;
                width: auto !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                text-align: left !important;
            }

            .date-line {
                font-size: 14px !important;
                margin-bottom: 10px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                line-height: 1.2 !important;
                font-weight: bold !important;
                text-align: left !important;
            }

            .client-info {
                margin-bottom: 12px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                font-size: 14px !important;
                border-bottom: 2px solid #000 !important;
                padding-bottom: 8px !important;
                line-height: 1.4 !important;
                font-weight: bold !important;
                text-align: left !important;
            }

            .client-info div {
                margin-bottom: 5px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                font-weight: bold !important;
                text-align: left !important;
            }
            
            .products-table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin-bottom: 12px !important;
                margin-top: 8px !important;
                margin-left: 0 !important;
                font-size: 13px !important;
                font-weight: bold !important;
            }

            .products-table th,
            .products-table td {
                border: 1px solid #000 !important;
                padding: 1px 1px !important;
                text-align: center !important;
                font-size: 13px !important;
                line-height: 1.1 !important;
                height: auto !important;
                font-weight: bold !important;
                vertical-align: middle !important;
            }

            .products-table th {
                font-weight: bold !important;
                background-color: #f5f5f5 !important;
                padding: 1px 1px !important;
                text-align: center !important;
                height: 18px !important;
            }

            .products-table td:first-child {
                text-align: left !important;
                font-size: 13px !important;
                padding-left: 1px !important;
                padding-right: 1px !important;
                font-weight: bold !important;
            }

            .products-table tr {
                height: 16px !important;
                font-weight: bold !important;
            }
            
            .total-info {
                text-align: left !important;
                font-weight: bold !important;
                margin-top: 12px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                border-top: 2px solid #000 !important;
                padding-top: 8px !important;
                font-size: 16px !important;
                line-height: 1.3 !important;
            }

            .prices-section {
                margin-top: 12px !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                font-size: 14px !important;
                padding-bottom: 8px !important;
                font-weight: bold !important;
                text-align: left !important;
            }

            .price-sum {
                text-align: left !important;
                font-weight: bold !important;
                margin-bottom: 4px !important;
                margin-left: 0 !important;
                padding: 2px !important;
                padding-left: 0 !important;
                border-bottom: 1px solid #ccc !important;
                line-height: 1.2 !important;
            }

            /* Газированные продукты – чёрный фон, белый текст (печать) */
            .products-table tr.gaz-row td {
                background: #000 !important;
                color: #fff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>

    <div class="print-invoice-header">
        <div class="print-invoice-number">
            <input type="number" class="editable-number" id="print-number" value="<?= $printNumber ?>" min="1" max="9999" data-sale-id="<?= $sales->id ?>" />
        </div>
        <div class="date-line"><?= date('d/m/Y H:i:s') ?></div>
    </div>

    <div class="client-info">
        <div><strong>КЛИЕНТ:</strong><br><?= Html::encode($sales->client->full_name) ?></div>
        <div><strong>АВТОМОБИЛЬ:</strong> <?= Html::encode($sales->car_number) ?></div>
        <div><strong>ВОДИТЕЛЬ:</strong> <?= Html::encode($sales->driver) ?></div>
    </div>

    <!-- Таблица продукции -->
    <table class="products-table">
        <thead>
            <tr>
                <th style="width: 50%;">ПРОДУКТ</th>
                <th style="width: 25%;">БЛОКИ</th>
                <th style="width: 25%;">ШТ</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            // Порядок отображения как на фото
            $displayOrder = ['0.33', '0.5 gaz', '0.5 bezgaz', '1 gaz', '1 bezgaz', '1.5 gaz', '1.5 bezgaz', '5', '10'];
            $displayNames = [
                '0.33' => '0,33 без газ',
                '0.5 gaz' => '0.5 литр газ', 
                '0.5 bezgaz' => '0.5 без газ',
                '1 gaz' => '1,0 литр газ',
                '1 bezgaz' => '1,0 без газ', 
                '1.5 gaz' => '1.5 литр газ',
                '1.5 bezgaz' => '1.5 без газ',
                '5' => '5 литр',
                '10' => '10 литр'
            ];
            
            // Показываем все продукты (если не покупали — «-»)
            foreach ($displayOrder as $type):
                $hasItem = isset($printData['items'][$type]);
                $blocks   = $hasItem ? number_format($printData['items'][$type]['blocks'], 0)   : '-';
                $quantity = $hasItem ? number_format($printData['items'][$type]['quantity'], 0) : '-';

                // Определяем, газированный ли продукт (без «bezgaz»)
                $rowClass = (strpos($type, 'gaz') !== false && strpos($type, 'bezgaz') === false) ? 'gaz-row' : '';
            ?>
            <tr class="<?= $rowClass ?>">
                <td><?= $displayNames[$type] ?></td>
                <td><?= $blocks ?></td>
                <td><?= $quantity ?></td>
            </tr>
            <?php 
            endforeach; 
            
            // Показываем бонусные продукты если есть
            if (!empty($printData['bonusItems'])):
            ?>
            <tr style="border-top: 2px solid #000;">
                <td colspan="3" style="text-align: center; font-weight: bold;">БОНУС</td>
            </tr>
            <?php
                foreach ($displayOrder as $type):
                    if (isset($printData['bonusItems'][$type])):
                        $blocks = $printData['bonusItems'][$type]['blocks'];
                        $quantity = $printData['bonusItems'][$type]['quantity'];

                        $rowClass = (strpos($type, 'gaz') !== false && strpos($type, 'bezgaz') === false) ? 'gaz-row' : '';
            ?>
            <tr class="<?= $rowClass ?>">
                <td><?= $displayNames[$type] ?></td>
                <td><?= number_format($blocks, 0) ?></td>
                <td><?= number_format($quantity, 0) ?></td>
            </tr>
            <?php 
                    endif;
                endforeach;
            endif;
            ?>
        </tbody>
    </table>

    <div class="total-info">
        <div>ВЕС: <?= number_format($printData['totalWeight'], 0) ?></div>
    </div>

    <!-- Итоговые суммы по ценам -->
    <div class="prices-section">
        <?php 
        // Рассчитываем итоговые суммы по каждому типу цен
        $priceLabels = ['1-narx', '2-narx', '3-narx', '4-narx', '5-narx'];
        
        foreach ($priceLabels as $priceType):
            $totalSum = 0;
            
            // Рассчитываем сумму для обычных продуктов
            foreach ($printData['items'] as $type => $data) {
                $quantity = $data['quantity'];
                $price = $printData['prices'][$type][$priceType] ?? 0;
                $totalSum += $quantity * $price;
            }
            
           
        ?>
        <div class="price-sum"><?= number_format($totalSum, 2, ',', ' ') ?></div>
        <?php endforeach; ?>
    </div>

    <div class="no-print">
        <button type="button" class="btn btn-success btn-sm" onclick="printInvoice()">
            <i class="fas fa-print"></i> Печать
        </button>
        <div id="save-status" class="mt-2"></div>
    </div>
</div>

<script>
// Функция для печати накладной
function printInvoice() {
    // Получаем текущее значение номера накладной
    var invoiceNumber = document.getElementById('print-number').value;

    // Проверяем, что номер накладной введён
    if (!invoiceNumber || invoiceNumber == '0' || invoiceNumber.trim() === '') {
        alert('Пожалуйста, введите номер накладной перед печатью!');
        document.getElementById('print-number').focus();
        return;
    }
    
    // Сохраняем номер корешка перед печатью
    savePrintNumber();

    // Создаем новое окно для печати
    var printWindow = window.open('', '_blank', 'width=800,height=600');

    // Получаем содержимое накладной и обновляем номер
    var invoiceContent = document.querySelector('.print-invoice-view').outerHTML;
    // Заменяем значение номера в HTML
    invoiceContent = invoiceContent.replace(/(<input[^>]*id="print-number"[^>]*value=")[^"]*(")/i, '$1' + invoiceNumber + '$2');

    // Создаем HTML для печати
    var printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Печатная накладная</title>
            <style>
                @page {
                    size: A4 portrait;
                    margin: 5mm;
                }

                @page:first {
                    size: A4 portrait;
                    margin: 5mm;
                }

                body {
                    margin: 0;
                    padding: 0;
                    background: white;
                    color: black;
                    font-family: 'Courier New', monospace;
                    width: 100vw;
                    height: 100vh;
                    max-height: 240mm;
                    overflow: hidden;
                    font-weight: bold;
                }

                .print-invoice-view {
                    position: absolute;
                    top: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    margin: 0;
                    padding: 2mm;
                    width: 25%;
                    max-width: 25%;
                    height: 240mm;
                    max-height: 240mm;
                    background: white;
                    box-shadow: none;
                    border: none;
                    font-size: 14px;
                    line-height: 1.3;
                    display: block;
                    overflow: hidden;
                    page-break-after: avoid;
                    page-break-inside: avoid;
                    break-after: avoid;
                    break-inside: avoid;
                    box-sizing: border-box;
                    font-weight: bold;
                    text-align: left;
                }

                .print-invoice-view * {
                    page-break-after: avoid;
                    page-break-inside: avoid;
                    break-after: avoid;
                    break-inside: avoid;
                    font-weight: bold;
                }

                .print-invoice-header {
                    text-align: left;
                    margin-bottom: 12px;
                    margin-left: 0;
                    padding-left: 0;
                    border-bottom: 2px solid #000;
                    padding-bottom: 8px;
                    font-weight: bold;
                }

                .print-invoice-number {
                    font-size: 22px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    margin-left: 0;
                    padding-left: 0;
                    text-align: left;
                }

                .editable-number {
                    font-size: 20px;
                    font-weight: bold;
                    border: none;
                    background: transparent;
                    width: auto;
                    margin-left: 0;
                    padding-left: 0;
                    text-align: left;
                }

                .date-line {
                    font-size: 14px;
                    margin-bottom: 10px;
                    margin-left: 0;
                    padding-left: 0;
                    line-height: 1.2;
                    font-weight: bold;
                    text-align: left;
                }

                .client-info {
                    margin-bottom: 12px;
                    margin-left: 0;
                    padding-left: 0;
                    font-size: 14px;
                    border-bottom: 2px solid #000;
                    padding-bottom: 8px;
                    line-height: 1.4;
                    font-weight: bold;
                    text-align: left;
                }

                .client-info div {
                    margin-bottom: 5px;
                    margin-left: 0;
                    padding-left: 0;
                    font-weight: bold;
                    text-align: left;
                }

                .products-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 12px;
                    margin-top: 8px;
                    margin-left: 0;
                    font-size: 13px;
                    font-weight: bold;
                }

                .products-table th,
                .products-table td {
                    border: 1px solid #000;
                    padding: 1px 1px;
                    text-align: center;
                    font-size: 13px;
                    line-height: 1.1;
                    height: auto;
                    font-weight: bold;
                    vertical-align: middle;
                }

                .products-table th {
                    font-weight: bold;
                    background-color: #f5f5f5;
                    padding: 1px 1px;
                    text-align: center;
                    height: 18px;
                }

                .products-table td:first-child {
                    text-align: left;
                    font-size: 11px;
                    padding-left: 1px;
                    padding-right: 1px;
                    font-weight: bold;
                }

                /* Газированные продукты – чёрный фон, белый текст */
                .products-table tr.gaz-row td {
                    background: #000;
                    color: #fff;
                }

                .products-table tr {
                    height: 16px;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            ${invoiceContent}
        </body>
        </html>
    `;

    // Записываем HTML в новое окно
    printWindow.document.write(printHTML);
    printWindow.document.close();

    // Ждем загрузки и печатаем
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}

// Функция для сохранения номера корешка
function savePrintNumber() {
    var printNumber = document.getElementById('print-number').value;
    var saleId = document.getElementById('print-number').getAttribute('data-sale-id');
    var saveStatus = document.getElementById('save-status');
    
    if (!printNumber || printNumber == '0' || printNumber.trim() === '') {
        saveStatus.innerHTML = '<div class="alert alert-warning">Введите номер корешка</div>';
        return false;
    }
    
    // Показываем индикатор загрузки
    saveStatus.innerHTML = '<div class="text-info">Сохранение...</div>';
    
    // Отправляем AJAX запрос
    $.ajax({
        url: '<?= \yii\helpers\Url::to(['/backend/print-invoice/save-print-number']) ?>',
        type: 'POST',
        data: {
            id: saleId,
            print_number: printNumber
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                saveStatus.innerHTML = '<div class="alert alert-success">' + response.message + '</div>';
                
                // Скрываем сообщение через 3 секунды
                setTimeout(function() {
                    saveStatus.innerHTML = '';
                }, 3000);
            } else {
                saveStatus.innerHTML = '<div class="alert alert-danger">' + response.message + '</div>';
            }
        },
        error: function() {
            saveStatus.innerHTML = '<div class="alert alert-danger">Ошибка при сохранении</div>';
        }
    });
    
    return true;
}

$(document).ready(function() {
    // Обработчик события печати
    window.addEventListener('beforeprint', function() {
        document.body.style.background = 'white';
        document.body.style.backgroundColor = 'white';
    });
    
    // Обработчик изменения номера корешка
    $('#print-number').on('change', function() {
        savePrintNumber();
    });
});
</script>